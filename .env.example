# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# OpenRouter API Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_MODEL=google/gemini-2.5-flash-preview:thinking

# Database Configuration
DATABASE_TYPE=sqlite
DATABASE_PATH=mealmetrics.db

# MySQL Configuration (for production deployment)
# Set DATABASE_TYPE=mysql to use MySQL instead of SQLite
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_mysql_username
MYSQL_PASSWORD=your_mysql_password
MYSQL_DATABASE=mealmetrics

# Bot Configuration
MAX_IMAGE_SIZE_MB=10
SUPPORTED_IMAGE_FORMATS=jpg,jpeg,png,webp
